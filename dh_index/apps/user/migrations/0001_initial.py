# Generated by Django 5.2.4 on 2025-07-14 03:58

import dh_index.apps.user.models
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('password', models.CharField(blank=True, max_length=128)),
                ('username', models.CharField(max_length=128, unique=True)),
                ('full_name', models.CharField(max_length=128, null=True)),
                ('avatar', models.CharField(max_length=256, null=True)),
                ('settings', models.J<PERSON><PERSON>ield(blank=True, default=dict, null=True)),
                ('is_active', models.BooleanField(blank=True, default=False)),
                ('verify_code', models.CharField(blank=True, max_length=10)),
                ('device_tokens', models.JSONField(blank=True, default=dict, null=True)),
                ('is_delete', models.BooleanField(blank=True, default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'abstract': False,
            },
            managers=[
                ('objects', dh_index.apps.user.models.CustomUserManager()),
            ],
        ),
    ]
