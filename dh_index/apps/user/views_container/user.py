from dh_index.apps.user.serializers import (
    UserDetailSerializer
)
from dh_index.apps.user.views_container import (
    Response, permissions, APIView, swagger_auto_schema, openapi
)


class UserDetailViewSet(APIView):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = UserDetailSerializer

    @staticmethod
    def get(request, *args, **kwargs):
        serializer = UserDetailSerializer(request.user, context={'request': request})
        return Response(serializer.data)



